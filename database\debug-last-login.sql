-- ================================
-- DEBUG: LAST LOGIN KONTROLÜ
-- ================================
-- Bu dosya last_login alanının güncellenip güncellenmediğini kontrol eder

-- 1. <PERSON><PERSON><PERSON> kull<PERSON>ın last_login durumunu göster
SELECT 
  id,
  email,
  full_name,
  role,
  status,
  created_at,
  last_login,
  CASE 
    WHEN last_login IS NULL THEN 'Hiç giriş yapmamış'
    WHEN last_login < NOW() - INTERVAL '1 hour' THEN 'Eski giriş (' || EXTRACT(EPOCH FROM (NOW() - last_login))/3600 || ' saat önce)'
    WHEN last_login < NOW() - INTERVAL '1 minute' THEN 'Yakın giriş (' || EXTRACT(EPOCH FROM (NOW() - last_login))/60 || ' dakika önce)'
    ELSE 'Çok yakın giriş (' || EXTRACT(EPOCH FROM (NOW() - last_login)) || ' saniye önce)'
  END as login_status
FROM users 
ORDER BY last_login DESC NULLS LAST;

-- 2. Son 1 saatte giriş yapan kullanıcılar
SELECT 
  email,
  full_name,
  role,
  last_login,
  NOW() - last_login as time_since_login
FROM users 
WHERE last_login > NOW() - INTERVAL '1 hour'
ORDER BY last_login DESC;

-- 3. Hiç giriş yapmamış kullanıcılar
SELECT 
  email,
  full_name,
  role,
  created_at
FROM users 
WHERE last_login IS NULL
ORDER BY created_at DESC;

-- 4. Test kullanıcılarının durumu
SELECT 
  email,
  full_name,
  role,
  last_login,
  CASE 
    WHEN email = '<EMAIL>' THEN 'Admin Test Kullanıcısı'
    WHEN email = '<EMAIL>' THEN 'Sürücü Test Kullanıcısı'
    ELSE 'Diğer Kullanıcı'
  END as user_type
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY email;
