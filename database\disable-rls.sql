-- ================================
-- DISABLE RLS POLICIES
-- ================================
-- Bu dosya RLS politikalarını kaldırır ve basit güvenlik modeline geçer

-- ================================
-- 1. USERS TABLE RLS DISABLE
-- ================================

-- Mevcut politikaları kaldır
DROP POLICY IF EXISTS "Allow read access to users" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile by email" ON users;
DROP POLICY IF EXISTS "Allow user creation" ON users;
DROP POLICY IF EXISTS "Service role can delete users" ON users;

-- RLS'i tamamen kapat
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- ================================
-- 2. DİĞER TABLOLAR RLS DISABLE
-- ================================

-- Lookup values
DROP POLICY IF EXISTS "Allow read lookup values" ON lookup_values;
ALTER TABLE lookup_values DISABLE ROW LEVEL SECURITY;

-- Places
DROP POLICY IF EXISTS "Allow read places" ON places;
ALTER TABLE places DISABLE ROW LEVEL SECURITY;

-- Deceased
DROP POLICY IF EXISTS "Authenticated users can access deceased" ON deceased;
ALTER TABLE deceased DISABLE ROW LEVEL SECURITY;

-- Cases
DROP POLICY IF EXISTS "Authenticated users can access cases" ON cases;
ALTER TABLE cases DISABLE ROW LEVEL SECURITY;

-- Documents
DROP POLICY IF EXISTS "Authenticated users can access documents" ON documents;
ALTER TABLE documents DISABLE ROW LEVEL SECURITY;

-- Notifications
DROP POLICY IF EXISTS "Authenticated users can access notifications" ON notifications;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- Tasks
DROP POLICY IF EXISTS "Authenticated users can access tasks" ON tasks;
ALTER TABLE tasks DISABLE ROW LEVEL SECURITY;

-- Driver locations
DROP POLICY IF EXISTS "Authenticated users can access driver_locations" ON driver_locations;
ALTER TABLE driver_locations DISABLE ROW LEVEL SECURITY;

-- Task events
DROP POLICY IF EXISTS "Authenticated users can access task_events" ON task_events;
ALTER TABLE task_events DISABLE ROW LEVEL SECURITY;

-- ================================
-- 3. KONTROL
-- ================================

-- RLS durumunu kontrol et
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('users', 'deceased', 'cases', 'tasks', 'documents', 'notifications', 'places', 'lookup_values', 'driver_locations', 'task_events')
ORDER BY tablename;

-- Kalan politikaları kontrol et
SELECT 
  schemaname,
  tablename,
  policyname,
  cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
