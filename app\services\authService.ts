import { supabase, supabaseAdmin } from '../lib/supabase';
import i18n from '../i18n';

export interface User {
  id: string;
  email: string;
  full_name?: string;
  role?: 'ADMIN' | 'DRIVER' | 'FAMILY';
  phone?: string;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  error?: string;
  message?: string;
  needsAccountCreation?: boolean;
}

export interface UserStatusResult {
  status: 'NOT_FOUND' | 'NEEDS_PASSWORD' | 'HAS_PASSWORD';
  user?: any;
  message?: string;
}

interface Profile {
  id: string;
  email: string;
  full_name: string;
  role: 'ADMIN' | 'DRIVER' | 'FAMILY';
  phone?: string;
}

interface InvitationResponse {
  success: boolean;
  invitation?: any;
  error?: string;
  message?: string;
}

interface AcceptInvitationResponse {
  success: boolean;
  user_id?: string;
  message?: string;
  error?: string;
}

class AuthService {
  // Auth service'i başlat ve geçersiz session'ları temizle
  async initialize(): Promise<void> {
    try {
      console.log('AuthService: Initializing...');

      // Mevcut session'ı kontrol et
      const { data, error } = await supabase.auth.getSession();

      // Eğer refresh token hatası varsa session'ı temizle
      if (error && error.message.includes('Invalid Refresh Token')) {
        console.log('AuthService: Invalid refresh token detected during initialization, clearing...');
        await this.clearInvalidSession();
      }

      console.log('AuthService: Initialization complete');
    } catch (error) {
      console.error('AuthService: Initialization error:', error);
    }
  }

  // Supabase bağlantısını test et
  async testConnection(): Promise<boolean> {
    try {
      console.log('AuthService: Testing Supabase connection...');
      // RLS policy sorunları nedeniyle basit bir auth test yap
      const { data, error } = await supabase.auth.getSession();

      // Eğer refresh token hatası varsa session'ı temizle
      if (error && error.message.includes('Invalid Refresh Token')) {
        console.log('AuthService: Invalid refresh token detected, clearing session...');
        await this.clearInvalidSession();
        return true; // Bağlantı çalışıyor, sadece session temizlendi
      }

      console.log('AuthService: Connection test result:', { data: data ? 'session_exists' : 'no_session', error });
      return !error;
    } catch (error) {
      console.log('AuthService: Connection test failed:', error);
      return false;
    }
  }

  // Geçersiz session'ı temizle
  async clearInvalidSession(): Promise<void> {
    try {
      console.log('AuthService: Clearing invalid session...');
      await supabase.auth.signOut();
      // AsyncStorage'dan tüm auth verilerini temizle
      await this.clearAllAuthData();
      console.log('AuthService: Invalid session cleared');
    } catch (error) {
      console.error('AuthService: Error clearing invalid session:', error);
    }
  }

  // Tüm auth verilerini AsyncStorage'dan temizle
  async clearAllAuthData(): Promise<void> {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const keys = [
        'supabase.auth.token',
        'sb-wvhyedtgiwknjjayubbt-auth-token', // Supabase'in kullandığı key format
        '@supabase/auth-token',
      ];

      for (const key of keys) {
        await AsyncStorage.removeItem(key);
      }

      console.log('AuthService: All auth data cleared from AsyncStorage');
    } catch (error) {
      console.error('AuthService: Error clearing auth data:', error);
    }
  }

  // Veri tabanı durumunu kontrol et
  async checkDatabaseStatus(): Promise<{ hasUsers: boolean; userCount: number; error?: string }> {
    try {
      console.log('AuthService: Checking database status...');

      const { data: users, error } = await supabase
        .from('users')
        .select('id, email')
        .limit(10);

      if (error) {
        console.log('AuthService: Database check error:', error);
        return {
          hasUsers: false,
          userCount: 0,
          error: error.message
        };
      }

      console.log('AuthService: Database status:', { userCount: users?.length || 0 });
      return {
        hasUsers: (users?.length || 0) > 0,
        userCount: users?.length || 0
      };
    } catch (error) {
      console.error('AuthService: Database status check failed:', error);
      return {
        hasUsers: false,
        userCount: 0,
        error: 'Database connection failed'
      };
    }
  }

  // MERKEZI KULLANICI DURUMU KONTROLÜ
  // Bu fonksiyon tüm kullanıcı durumu kontrollerini tek noktada yapar
  async checkUserStatus(email: string): Promise<UserStatusResult> {
    try {
      console.log('AuthService: Checking user status for:', email);

      // 1. Users tablosunda kullanıcı var mı kontrol et
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('email', email.trim().toLowerCase())
        .single();

      if (userError || !user) {
        console.log('AuthService: User not found in users table');
        return {
          status: 'NOT_FOUND',
          message: i18n.t('auth.emailNotRegistered')
        };
      }

      // 2. Auth.users tablosunda kullanıcı kontrolü - Admin API ile kontrol et
      try {
        console.log('AuthService: Checking auth.users table with admin API...');
        
        // Admin API ile kullanıcı listesini al ve email ile filtrele
        const { data: authUsers, error: adminError } = await supabaseAdmin.auth.admin.listUsers({
          page: 1,
          perPage: 1000 // Yeterli sayıda kullanıcı getir
        });
        
        if (adminError) {
          console.log('AuthService: Admin API error, assuming needs password:', adminError.message);
          return {
            status: 'NEEDS_PASSWORD',
            user: user,
            message: 'Şifre oluşturması gerekiyor'
          };
        }
        
        // Email ile kullanıcıyı ara - Type assertion ile optimize edilmiş çözüm
        const authUser = authUsers?.users?.find((u: any) => 
          u?.email?.toLowerCase() === email.trim().toLowerCase()
        ) || null;
        
        if (authUser) {
          console.log('AuthService: User found in auth.users via admin API');
          return {
            status: 'HAS_PASSWORD',
            user: user,
            message: 'Kullanıcı giriş yapabilir'
          };
        } else {
          console.log('AuthService: User not found in auth.users via admin API');
          return {
            status: 'NEEDS_PASSWORD',
            user: user,
            message: 'Şifre oluşturması gerekiyor'
          };
        }
      } catch (checkError) {
        console.log('AuthService: Auth.users check failed, assuming needs password:', checkError);
        return {
          status: 'NEEDS_PASSWORD',
          user: user,
          message: 'Şifre oluşturması gerekiyor'
        };
      }
    } catch (error) {
      console.error('AuthService: User status check error:', error);
      return {
        status: 'NOT_FOUND',
        message: 'Kontrol yapılamadı. Lütfen tekrar deneyin.'
      };
    }
  }

  // Email kontrolü - User var mı ve daha önce şifre aldı mı?
  // Bu fonksiyon artık merkezi checkUserStatus fonksiyonunu kullanır
  async checkUserByEmail(email: string): Promise<InvitationResponse> {
    try {
      console.log('AuthService: Checking user for:', email);

      // Merkezi kullanıcı durumu kontrolünü kullan
      const userStatus = await this.checkUserStatus(email);

      switch (userStatus.status) {
        case 'NOT_FOUND':
          return {
            success: false,
            error: userStatus.message || i18n.t('auth.emailNotRegistered')
          };

        case 'HAS_PASSWORD':
          return {
            success: false,
            error: 'Bu e-posta adresi için zaten hesap oluşturulmuş. Lütfen giriş sayfasını kullanın.'
          };

        case 'NEEDS_PASSWORD':
          return {
            success: true,
            invitation: {
              email: userStatus.user.email,
              token: 'direct-signup',
              user_name: userStatus.user.full_name,
            },
          };

        default:
          return {
            success: false,
            error: 'Beklenmeyen bir durum oluştu.'
          };
      }
    } catch (error) {
      console.error('AuthService: Staff check error:', error);
      return {
        success: false,
        error: 'Kontrol yapılamadı. Lütfen tekrar deneyin.'
      };
    }
  }

  // Token ile davetiye getir - Basitleştirilmiş versiyon (invitations tablosu yok)
  async getInvitationByToken(token: string): Promise<InvitationResponse> {
    try {
      console.log('AuthService: Getting invitation by token (simplified)');

      // Yeni şemada invitations tablosu yok, basit token kontrolü
      if (token === 'direct-signup') {
        return {
          success: true,
          invitation: {
            token: token,
            email: 'direct-signup',
          },
        };
      }

      return {
          success: false,
          error: 'Geçersiz davetiye token\'ı'
        };
    } catch (error) {
      console.error('AuthService: Get invitation unexpected error:', error);
      return {
          success: false,
          error: 'Beklenmeyen bir hata oluştu'
        };
    }
  }

  // Basit kullanıcı oluşturma (invitation sistemi yerine)
  async createUserWithPassword(email: string, password: string): Promise<AcceptInvitationResponse> {
    try {
      console.log('AuthService: Creating user with email:', email);

      // Önce users tablosunda var mı kontrol et
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('email', email.trim().toLowerCase())
        .single();

      if (userError || !user) {
        console.log('AuthService: User not found in users table');
        return {
          success: false,
          message: i18n.t('auth.emailNotRegistered')
        };
      }

      console.log('AuthService: User found, checking if user already has password...');

      console.log('AuthService: Creating new auth user...');

      // Supabase auth ile kullanıcı oluştur - otomatik olarak auth.users'a eklenecek
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: email.trim().toLowerCase(),
        password: password,
        options: {
          emailRedirectTo: undefined, // Email confirmation'ı bypass et
          data: {
            full_name: user.full_name,
            role: user.role,
            phone: user.phone || null, // Phone bilgisini de ekle
          }
        }
      });

      if (authError) {
        console.error('AuthService: Auth signup error:', authError);
        console.error('AuthService: Full error object:', JSON.stringify(authError, null, 2));

        // Kullanıcı zaten varsa - bu durumda giriş yapmalarını söyle
        if (authError.message.includes('already registered') ||
            authError.message.includes('already been registered') ||
            authError.message.includes('User already registered')) {
          return {
              success: false,
              message: i18n.t('auth.passwordAlreadySet')
            };
        }

        // Email validation hatası - Supabase'in katı kuralları
        if (authError.message.includes('invalid') || authError.message.includes('Email address')) {
          return {
              success: false,
              message: i18n.t('auth.invalidEmailFormat')
            };
        }

        // Email confirmation gerekli hatası
        if (authError.message.includes('email') && authError.message.includes('confirm')) {
          return {
              success: false,
              message: 'E-posta doğrulaması gerekli. Lütfen e-postanızı kontrol edin.'
            };
        }

        return {
            success: false,
            message: 'Kullanıcı oluşturulamadı: ' + authError.message
          };
      }

      console.log('AuthService: User created successfully!');
      console.log('AuthService: Created user data:', authData.user);

      // Auth user başarıyla oluşturuldu, şimdi users tablosundaki ID'yi güncelle ve last_login'i ayarla
      if (authData.user?.id) {
        const loginTime = new Date().toISOString();
        console.log('AuthService: 🔄 Updating users table ID to match auth.users ID...');
        console.log('AuthService: 📅 Setting initial last_login timestamp:', loginTime);

        const { data: updateData, error: updateError } = await supabase
          .from('users')
          .update({
            id: authData.user.id,
            last_login: loginTime // Hesap oluşturma sırasında last_login'i ayarla
          })
          .eq('email', email.trim().toLowerCase())
          .select('email, full_name, last_login');

        if (updateError) {
          console.error('AuthService: ❌ Error updating users table ID and last_login:', updateError);
          console.error('AuthService: ❌ Update error details:', JSON.stringify(updateError, null, 2));
          // Bu hata kritik değil, kullanıcı oluşturuldu ama ID eşleşmedi
          // Yine de başarılı olarak döndür
        } else {
          console.log('AuthService: ✅ Users table ID and last_login updated successfully');
          console.log('AuthService: 📊 Updated user data after account creation:', updateData);
          if (updateData && updateData.length > 0) {
            console.log('AuthService: 🎯 Confirmed last_login in DB after account creation:', updateData[0].last_login);
            console.log('AuthService: 👤 New user:', updateData[0].email, '-', updateData[0].full_name);
          }
        }
      }

      return {
          success: true,
          message: 'Hesabınız başarıyla oluşturuldu! Artık giriş yapabilirsiniz.'
        };

    } catch (error) {
      console.log('AuthService: Create user unexpected error (handled safely):', error);
      // Beklenmeyen hata durumunda bile kullanıcı dostu mesaj - ASLA ERROR DEĞİL!
      return {
          success: false,
          message: 'Hesap oluşturulamadı. Lütfen tekrar deneyin veya yöneticinizle iletişime geçin.'
        };
    }
  }

  // Email/Password ile giriş - Merkezi sistem kullanarak
  async signInWithPassword(email: string, password: string): Promise<AuthResponse> {
    try {
      console.log('AuthService: Starting login for:', email);

      // Önce mevcut session'ı temizle (cache sorunları için)
      console.log('AuthService: Clearing existing session...');
      await supabase.auth.signOut();

      // 1. Önce users tablosunda kullanıcı var mı kontrol et
      console.log('AuthService: Checking if user exists in users table...');
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('email', email.trim().toLowerCase())
        .single();

      console.log('AuthService: Users table query result:', {
        user: user ? { id: user.id, email: user.email, full_name: user.full_name, role: user.role } : null,
        userError: userError ? { code: userError.code, message: userError.message } : null
      });

      if (userError || !user) {
        console.log('AuthService: User not found in users table, error:', userError?.message);
        console.log('AuthService: Searched email (normalized):', email.trim().toLowerCase());

        // Eğer tablo bulunamadı hatası varsa, daha açıklayıcı mesaj ver
        if (userError?.code === '42P01') {
          return {
            success: false,
            message: 'Veritabanı tablosu bulunamadı. Lütfen database/04-test-data.sql dosyasını Supabase\'de çalıştırın.'
          };
        }

        // PGRST116 = No rows returned
        if (userError?.code === 'PGRST116') {
          return {
            success: false,
            message: `E-posta adresi "${email}" sistemde kayıtlı değil. Lütfen database/debug-users.sql dosyasını çalıştırarak kullanıcıları kontrol edin.`
          };
        }

        return {
          success: false,
          message: 'Geçersiz kullanıcı. E-posta adresiniz sistemde kayıtlı değil.'
        };
      }

      console.log('AuthService: User found in users table:', user.full_name);

      // 2. Auth.users tablosunda kullanıcı kontrolü - Admin API ile
      let userExistsInAuthUsers = false;
      
      try {
        console.log('AuthService: Checking auth.users table with admin API...');
        
        // Admin API ile kullanıcı listesini al ve email ile filtrele
        const { data: authUsers, error: adminError } = await supabaseAdmin.auth.admin.listUsers({
          page: 1,
          perPage: 1000 // Yeterli sayıda kullanıcı getir
        });
        
        if (adminError) {
          console.log('AuthService: Admin API error, falling back to resetPasswordForEmail:', adminError.message);
          
          // Admin API başarısız olursa resetPasswordForEmail ile kontrol et
          try {
            const { error: resetError } = await supabase.auth.resetPasswordForEmail(
              email.trim().toLowerCase(),
              { redirectTo: 'https://dummy-redirect.com' }
            );
            
            if (resetError) {
              // Reset password başarısız = kullanıcı auth.users'da yok
              console.log('AuthService: Reset password failed, user not in auth.users:', resetError.message);
              userExistsInAuthUsers = false;
            } else {
              // Reset password başarılı = kullanıcı auth.users'da var
              console.log('AuthService: Reset password successful, user exists in auth.users');
              userExistsInAuthUsers = true;
            }
          } catch (resetCheckError) {
            console.log('AuthService: Reset password check failed:', resetCheckError);
            userExistsInAuthUsers = false;
          }
        } else if (authUsers?.users) {
          // Admin API başarılı - kullanıcıyı email ile ara - Type assertion ile optimize edilmiş
          const userInAuth = authUsers.users.find((authUser: any) => 
            authUser?.email?.toLowerCase() === email.trim().toLowerCase()
          );
          
          if (userInAuth) {
            console.log('AuthService: User found in auth.users via admin API:', userInAuth.id);
            userExistsInAuthUsers = true;
          } else {
            console.log('AuthService: User not found in auth.users via admin API');
            userExistsInAuthUsers = false;
          }
        } else {
          console.log('AuthService: Invalid authUsers response structure');
          userExistsInAuthUsers = false;
        }
      } catch (checkError) {
        console.log('AuthService: Auth.users check completely failed:', checkError);
        userExistsInAuthUsers = false;
      }

      // 3. Kullanıcı durumuna göre aksiyon al
      if (userExistsInAuthUsers) {
        // Kullanıcı hem users'da hem auth.users'da var - şifre kontrolü yap
        console.log('AuthService: User exists in both tables, checking password...');
      } else {
        // Kullanıcı users'da var ama auth.users'da yok - hesap oluşturma yönlendir
        console.log('AuthService: User exists in users table but not in auth.users, needs account creation');
        return {
          success: false,
          message: 'Şifre almak için Hesap Oluştur sayfasına gidin.',
          needsAccountCreation: true
        };
      }

      // 4. Kullanıcı auth.users'da var, şifre ile giriş yapmayı dene
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password,
      });

      if (authError) {
        console.log('AuthService: Auth failed:', authError.message);
        
        if (authError.message.includes('Invalid login credentials')) {
          return {
            success: false,
            message: 'Yanlış şifre. Lütfen tekrar deneyin.'
          };
        }
        
        return {
          success: false,
          message: i18n.t('auth.loginFailed') + ': ' + authError.message
        };
      }

      // Başarılı giriş
      if (authData.user && user) {
        const loginTime = new Date().toISOString();
        console.log(`AuthService: Login successful at ${loginTime} for user: ${user.full_name} (${user.email})`);

        // Son giriş zamanını güncelle - auth.users ID'sini kullan
        await this.updateLastLogin(authData.user.id);

        const userResponse: User = {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          role: user.role,
          phone: user.phone,
        };

        return {
          success: true,
          user: userResponse,
          message: 'Giriş başarılı'
        };
      }

      return {
        success: false,
        message: i18n.t('auth.loginFailed')
      };
    } catch (error) {
      console.error('AuthService: Login error:', error);
      return {
        success: false,
        message: 'Beklenmeyen bir hata oluştu'
      };
    }
  }

  // Mevcut kullanıcıyı users tablosundan getir
  async getCurrentUser(): Promise<User | null> {
    try {
      console.log('AuthService: Getting current user...');

      // Önce auth user'ı al
      const { data: authUser, error: authError } = await supabase.auth.getUser();

      // Refresh token hatası varsa session'ı temizle
      if (authError && authError.message.includes('Invalid Refresh Token')) {
        console.log('AuthService: Invalid refresh token in getCurrentUser, clearing session...');
        await this.clearInvalidSession();
        return null;
      }

      if (!authUser.user) {
        console.log('AuthService: No authenticated user');
        return null;
      }

      console.log('AuthService: Auth user found, fetching from users table...');

      // Users tablosundan bilgileri çek
      const { data: user, error } = await supabase
          .from('users')
          .select('id, email, full_name, role, phone')
          .eq('email', authUser.user.email)
          .single();

      if (error) {
        console.log('AuthService: Error fetching user from users table:', error);
        // Fallback: auth metadata'dan profil oluştur
        return {
          id: authUser.user.id,
          email: authUser.user.email || '',
          full_name: authUser.user.user_metadata?.full_name || 'Kullanıcı',
          role: 'ADMIN',
          phone: authUser.user.user_metadata?.phone || null,
        };
      }

      console.log('AuthService: User fetched from users table:', user);
      return {
        id: authUser.user.id, // Auth user ID'sini kullan
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        phone: user.phone || null,
      };
    } catch (error) {
      console.error('AuthService: Get current user error:', error);
      return null;
    }
  }

  // Kullanıcı profili getir
  private async getProfile(userId: string): Promise<Profile | null> {
    try {
      // RLS policy sonsuz döngü sorunu için service_role kullanarak bypass et
      // Geçici çözüm - policy'ler düzeltildikten sonra kaldırılacak
      console.log('AuthService: Fetching profile for user:', userId);

      // RLS policy sorunu nedeniyle direkt fallback kullan
      console.log('AuthService: Using fallback profile due to RLS issues...');

      // Auth user bilgilerinden profil oluştur
      const { data: authUser } = await supabase.auth.getUser();
      if (authUser.user) {
        console.log('AuthService: Creating user profile from auth metadata');
        return {
          id: userId,
          email: authUser.user.email || '',
          full_name: authUser.user.user_metadata?.full_name || 'Kullanıcı',
          role: authUser.user.user_metadata?.role || 'ADMIN',
          phone: authUser.user.user_metadata?.phone || null,
        };
      }

      console.log('AuthService: No auth user found');
      return null;
    } catch (error) {
      console.error('AuthService: Profile fetch unexpected error:', error);
      return null;
    }
  }

  // Son giriş zamanını güncelle
  private async updateLastLogin(userId: string): Promise<void> {
    try {
      const loginTime = new Date().toISOString();
      console.log('AuthService: 🕐 Updating last login for user:', userId);
      console.log('AuthService: 📅 New last_login timestamp:', loginTime);

      const { data, error } = await supabase
        .from('users')
        .update({
          last_login: loginTime
        })
        .eq('id', userId)
        .select('email, full_name, last_login');

      if (error) {
        console.error('AuthService: ❌ Error updating last login:', error);
        console.error('AuthService: ❌ Error details:', JSON.stringify(error, null, 2));
      } else {
        console.log('AuthService: ✅ Last login updated successfully in database');
        console.log('AuthService: 📊 Updated user data:', data);
        if (data && data.length > 0) {
          console.log('AuthService: 🎯 Confirmed last_login in DB:', data[0].last_login);
          console.log('AuthService: 👤 User:', data[0].email, '-', data[0].full_name);
        }
      }
    } catch (error) {
      console.error('AuthService: ❌ Update last login unexpected error:', error);
    }
  }

  // Hata mesajlarını kullanıcı dostu hale getir ve user kontrolü ekle
  private async getErrorMessage(email: string, errorMessage: string): Promise<string> {
    if (errorMessage.includes('Invalid login credentials')) {
      // Kullanıcının users tablosunda olup olmadığını kontrol et
      try {
        console.log('AuthService: Checking users table for:', email);

        const { data: user, error: userError } = await supabase
          .from('users')
          .select('email, full_name')
          .eq('email', email.trim().toLowerCase())
          .single();

        console.log('AuthService: User check result:', { user, userError });

        // Tablo bulunamadı hatası (42P01)
        if (userError && userError.code === '42P01') {
          console.log('AuthService: Users table does not exist, using fallback logic');
          return 'Henüz şifre belirlememiş görünüyorsunuz. Lütfen "Hesap Oluştur" butonunu kullanın.';
        }

        // Kullanıcı bulunamadı hatası (PGRST116 - no rows returned)
        if (userError && (userError.code === 'PGRST116' || userError.message.includes('0 rows'))) {
          console.log('AuthService: User not found in users table');
          return 'Bu e-posta adresi sistemde kayıtlı değil. Lütfen yöneticinizle iletişime geçin.';
        }

        if (user && !userError) {
          // Users tablosunda var - şimdi auth.users'da var mı kontrol et
          try {
            console.log('AuthService: Checking auth.users table for password reset...');
            
            // Gerçek reset password denemesi yap - bu auth.users'da kullanıcının var olup olmadığını kontrol eder
            const { error: actualResetError } = await supabase.auth.resetPasswordForEmail(email.trim().toLowerCase());
            
            if (actualResetError) {
              // Eğer "User not found" hatası alırsak, kullanıcı auth.users'da yok demektir
              if (actualResetError.message.includes('User not found') || actualResetError.message.includes('Unable to validate email address')) {
                console.log('AuthService: User not found in auth.users for reset, needs password creation');
                return 'Henüz şifre belirlememiş görünüyorsunuz. Lütfen "Hesap Oluştur" butonunu kullanın.';
              } else {
                // Başka bir hata varsa (rate limit vs), genel hata mesajı ver
                console.log('AuthService: Reset password error:', actualResetError.message);
                return 'Şifre sıfırlama işlemi şu anda gerçekleştirilemiyor. Lütfen daha sonra tekrar deneyin.';
              }
            } else {
              // Reset email başarıyla gönderildi, kullanıcı auth.users'da var
              console.log('AuthService: Password reset email sent successfully');
              return 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi. Lütfen e-postanızı kontrol edin.';
            }
          } catch (checkError) {
            console.log('AuthService: Auth.users check failed for reset:', checkError);
            return 'Henüz şifre belirlememiş görünüyorsunuz. Lütfen "Hesap Oluştur" butonunu kullanın.';
          }
        } else {
          // Users tablosunda yok
          return 'Bu e-posta adresi sistemde kayıtlı değil. Lütfen yöneticinizle iletişime geçin.';
        }
      } catch (checkError: any) {
        console.error('AuthService: User check unexpected error:', checkError);

        // Beklenmeyen hata durumunda da şifre belirleme öner
        return 'Henüz şifre belirlememiş görünüyorsunuz. Lütfen "Hesap Oluştur" butonunu kullanın.';
      }
    } else if (errorMessage.includes('Email not confirmed')) {
      return 'E-posta adresiniz doğrulanmamış. Lütfen yöneticinizle iletişime geçin.';
    } else if (errorMessage.includes('Too many requests')) {
      return 'Çok fazla deneme yapıldı. Lütfen birkaç dakika bekleyin.';
    } else if (errorMessage.includes('User not found')) {
      return 'Bu e-posta adresi sistemde kayıtlı değil.';
    }
    return 'Giriş başarısız. Lütfen bilgilerinizi kontrol edin.';
  }



  // Magic Link ile giriş (basitleştirilmiş)
  async signInWithMagicLink(email: string): Promise<AuthResponse> {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: undefined, // Mobile app için gerekli değil
        },
      });

      if (error) {
        return { success: false, message: error.message };
      }

      return {
        success: true,
        message: 'Magic link e-posta adresinize gönderildi. E-postanızı kontrol edin.'
      };
    } catch (error) {
      return { success: false, message: 'Magic link gönderilemedi.' };
    }
  }

  // Çıkış
  async signOut(): Promise<void> {
    await supabase.auth.signOut();
  }

  // Gelişmiş çıkış işlemi
  async logout(): Promise<void> {
    try {
      console.log('AuthService: Logging out user at', new Date().toISOString());
      
      // Supabase auth çıkışı
      await supabase.auth.signOut();
      
      // AsyncStorage'dan auth verilerini temizle
      await this.clearAllAuthData();
      
      console.log('AuthService: User logged out successfully');
    } catch (error) {
      console.error('AuthService: Error during logout:', error);
      // Hata olsa bile çıkış yapmaya çalış
      try {
        await supabase.auth.signOut();
      } catch (innerError) {
        console.error('AuthService: Final logout attempt failed:', innerError);
      }
    }
  }

  // Debug: Kullanıcı bilgilerini detaylı göster
  async debugUserInfo(): Promise<any> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return { error: 'No authenticated user' };
      }

      // Auth.users bilgileri
      const authInfo = {
        id: user.id,
        email: user.email,
        user_metadata: user.user_metadata,
        app_metadata: user.app_metadata,
        created_at: user.created_at
      };

      // Users tablosundan bilgi
      let usersTableInfo = null;
      try {
        const { data: userData, error } = await supabase
          .from('users')
          .select('email, full_name')
          .eq('email', user.email)
          .single();

        usersTableInfo = { data: userData, error: error?.message };
      } catch (error) {
        usersTableInfo = { error: 'Users table query failed' };
      }

      return {
        auth_users: authInfo,
        users_table: usersTableInfo,
        current_user_result: await this.getCurrentUser()
      };
    } catch (error) {
      return { error: 'Debug failed', details: error };
    }
  }

  // Session temizle (debug için)
  async clearSession(): Promise<void> {
    try {
      await supabase.auth.signOut();
      console.log('AuthService: Session cleared');
    } catch (error) {
      console.error('AuthService: Session clear error:', error);
    }
  }

  // Test kullanıcılarını temizle (sadece development için)
  async cleanupTestUsers(): Promise<void> {
    try {
      console.log('AuthService: Cleaning up test users...');

      // Önce çıkış yap
      await supabase.auth.signOut();

      // Test email pattern'leri
      const testPatterns = [
        'temp-test-password',
        'temp-test-',
        'test-user-',
        'existence-check'
      ];

      console.log('AuthService: Test users cleanup completed');
    } catch (error) {
      console.error('AuthService: Test users cleanup error:', error);
    }
  }



  // Auth state değişikliklerini dinle
  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('AuthService: Auth state change event:', event);

      // Eğer token refresh hatası varsa session'ı temizle
      if (event === 'TOKEN_REFRESHED' && !session) {
        console.log('AuthService: Token refresh failed, clearing session...');
        await this.clearInvalidSession();
        callback(null);
        return;
      }

      if (session?.user) {
        // Users bilgilerini al
        try {
          const { data: userData, error } = await supabase
            .from('users')
            .select('full_name, role, phone')
            .eq('email', session.user.email)
            .single();

          if (error && error.code === '42P01') {
            // Users tablosu bulunamadı hatası
            console.log('onAuthStateChange: Users table not found, using fallback');
            const user: User = {
              id: session.user.id,
              email: session.user.email!,
              full_name: session.user.user_metadata?.full_name || 'Kullanıcı',
              role: 'ADMIN',
              phone: session.user.user_metadata?.phone || null,
            };
            callback(user);
            return;
          }

          if (userData && userData.full_name) {
            const user: User = {
              id: session.user.id,
              email: session.user.email!,
              full_name: userData.full_name,
              role: userData.role || 'ADMIN',
              phone: userData.phone || null,
            };
            callback(user);
          } else {
            // Users tablosunda kayıt yok ama auth.users'da var - fallback kullan
            console.log('onAuthStateChange: User not found in users table, using auth metadata');
            const user: User = {
              id: session.user.id,
              email: session.user.email!,
              full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'Kullanıcı',
              role: session.user.user_metadata?.role || 'ADMIN',
              phone: session.user.user_metadata?.phone || null,
            };
            callback(user);
          }
        } catch (error) {
          console.error('onAuthStateChange: Profile fetch error:', error);
          // Fallback - auth user bilgilerini kullan
          const user: User = {
            id: session.user.id,
            email: session.user.email!,
            full_name: session.user.user_metadata?.full_name || 'Kullanıcı',
            role: 'ADMIN',
            phone: session.user.user_metadata?.phone || null,
          };
          callback(user);
        }
      } else {
        callback(null);
      }
    });
  }
}

export const authService = new AuthService();
