-- ================================
-- ADD LOCATION COLUMN TO USERS TABLE
-- ================================
-- Bu dosya mevcut users tablosuna location kolonu ekler

-- Location kolonu ekle
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS location TEXT NULL;

-- Mevcut kullanıcılara default location değeri ata
UPDATE users 
SET location = 'Berlin' 
WHERE location IS NULL;

-- Kontrol et
SELECT 
  id,
  email,
  full_name,
  role,
  location,
  status,
  created_at
FROM users 
ORDER BY created_at DESC;

-- <PERSON><PERSON><PERSON> yapı<PERSON>ını kontrol et
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
  AND table_schema = 'public'
ORDER BY ordinal_position;
