import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { User } from '../../services/authService';
import { userService, UserData } from '../../services/userService';
import { useTranslation } from 'react-i18next';
import AddDriverModal from '../../components/AddDriverModal';

interface UsersManagementScreenProps {
  user: User;
  onBack: () => void;
}

const UsersManagementScreen = ({ user, onBack }: UsersManagementScreenProps) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'ALL' | 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE'>('ALL');
  const [loading, setLoading] = useState(true);
  const [drivers, setDrivers] = useState<UserData[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    loadDrivers();
  }, []);

  const loadDrivers = async () => {
    try {
      setLoading(true);
      const driversData = await userService.getAllDrivers();
      setDrivers(driversData);
    } catch (error) {
      console.error('Error loading drivers:', error);
      Alert.alert(t('common.error'), t('alerts.driversLoadError'));
    } finally {
      setLoading(false);
    }
  };

  const statusColors = {
    ACTIVE: '#2ECC71',
    ON_LEAVE: '#F39C12',
    INACTIVE: '#E74C3C',
  };

  const statusLabels = {
    ACTIVE: 'Aktif',
    ON_LEAVE: 'İzinli',
    INACTIVE: 'Pasif',
  };

  const filteredDrivers = drivers.filter(driver => {
    const matchesSearch = driver.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         driver.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         driver.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'ALL' || driver.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const handleAddDriver = () => {
    setShowAddModal(true);
  };

  const handleEditDriver = (driverId: string) => {
    Alert.alert('Sürücü Düzenle', `Sürücü düzenleme özelliği yakında eklenecek. ID: ${driverId}`);
  };

  const handleToggleStatus = (userId: string, currentStatus: string) => {
    // 3 durum arasında döngü: ACTIVE → ON_LEAVE → INACTIVE → ACTIVE
    let newStatus: string;
    let statusText: string;

    switch (currentStatus) {
      case 'ACTIVE':
        newStatus = 'ON_LEAVE';
        statusText = 'izinli';
        break;
      case 'ON_LEAVE':
        newStatus = 'INACTIVE';
        statusText = 'pasif';
        break;
      case 'INACTIVE':
        newStatus = 'ACTIVE';
        statusText = 'aktif';
        break;
      default:
        newStatus = 'ACTIVE';
        statusText = 'aktif';
    }

    Alert.alert(
      'Durum Değiştir',
      `Sürücü durumunu ${statusText} yapmak istediğinizden emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Evet',
          onPress: async () => {
            try {
              await userService.updateDriverStatus(userId, newStatus as 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE');
              // Listeyi yenile
              await loadDrivers();
              Alert.alert(t('common.success'), t('alerts.driverStatusUpdated'));
            } catch (error) {
              console.error('Error updating driver status:', error);
              Alert.alert(t('common.error'), t('alerts.statusUpdateError'));
            }
          }
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#0D3B66" />
        </TouchableOpacity>
        <Text style={styles.title}>Sürücü Yönetimi</Text>
        <TouchableOpacity onPress={handleAddDriver} style={styles.addButton}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={16} color="#8E9297" />
          <TextInput
            style={styles.searchInput}
            placeholder="Sürücü ara (isim, email, şehir)..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#8E9297"
          />
        </View>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          {(['ALL', 'ACTIVE', 'ON_LEAVE', 'INACTIVE'] as const).map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                selectedStatus === status && styles.filterButtonActive
              ]}
              onPress={() => setSelectedStatus(status)}
            >
              <Text style={[
                styles.filterButtonText,
                selectedStatus === status && styles.filterButtonTextActive
              ]}>
                {status === 'ALL' ? 'Tümü' : statusLabels[status]}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Drivers List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0D3B66" />
            <Text style={styles.loadingText}>Sürücüler yükleniyor...</Text>
          </View>
        ) : (
          <>
            <View style={styles.statsRow}>
              <Text style={styles.statsText}>
                {filteredDrivers.length} sürücü bulundu
              </Text>
            </View>

        {filteredDrivers.map((driverData) => (
          <View key={driverData.id} style={styles.userCard}>
            <View style={styles.userHeader}>
              <View style={styles.userInfo}>
                <View style={styles.userAvatar}>
                  <Text style={styles.userInitials}>
                    {driverData.full_name.split(' ').map(n => n[0]).join('')}
                  </Text>
                </View>
                <View style={styles.userDetails}>
                  <Text style={styles.userName}>{driverData.full_name}</Text>
                  <Text style={styles.userEmail} numberOfLines={1} ellipsizeMode="tail">{driverData.email}</Text>
                  <View style={styles.userMeta}>
                    <View style={[
                      styles.roleBadge,
                      { backgroundColor: '#2ECC71' }
                    ]}>
                      <Text style={styles.roleText}>Sürücü</Text>
                    </View>
                    <View style={[
                      styles.locationBadge,
                      { backgroundColor: '#0D3B66' }
                    ]}>
                      <Text style={styles.locationText}>{driverData.location}</Text>
                    </View>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: statusColors[driverData.status as keyof typeof statusColors] }
                    ]}>
                      <Text style={styles.statusText}>
                        {statusLabels[driverData.status as keyof typeof statusLabels]}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
              <View style={styles.userActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleEditDriver(driverData.id)}
                >
                  <Ionicons name="create" size={20} color="#0D3B66" />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    {
                      backgroundColor: driverData.status === 'ACTIVE' ? '#F39C12' :
                                     driverData.status === 'ON_LEAVE' ? '#E74C3C' : '#2ECC71'
                    }
                  ]}
                  onPress={() => handleToggleStatus(driverData.id, driverData.status)}
                >
                  <Ionicons
                    name={driverData.status === 'ACTIVE' ? 'time' :
                         driverData.status === 'ON_LEAVE' ? 'pause' : 'play'}
                    size={16}
                    color="#FFFFFF"
                  />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.userFooter}>
              <View style={styles.userStat}>
                <Ionicons name="call" size={14} color="#8E9297" />
                <Text style={styles.userStatText}>{driverData.phone}</Text>
              </View>
              <View style={styles.userStat}>
                <Ionicons name="calendar" size={14} color="#8E9297" />
                <Text style={styles.userStatText}>
                  Kayıt: {new Date(driverData.created_at).toLocaleDateString('tr-TR')}
                </Text>
              </View>
              <View style={styles.userStat}>
                <Ionicons name="time" size={14} color="#8E9297" />
                <Text style={styles.userStatText}>
                  Son giriş: {driverData.last_login ?
                    new Date(driverData.last_login).toLocaleDateString('tr-TR') :
                    'Hiç giriş yapmamış'}
                </Text>
              </View>
            </View>
          </View>
        ))}

        {filteredDrivers.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="car-outline" size={64} color="#E1E8ED" />
            <Text style={styles.emptyStateTitle}>Sürücü bulunamadı</Text>
            <Text style={styles.emptyStateText}>
              Arama kriterlerinize uygun sürücü bulunmuyor.
            </Text>
          </View>
        )}
          </>
        )}
      </ScrollView>

      {/* Add Driver Modal */}
      <AddDriverModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={loadDrivers}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F8FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 20 : (StatusBar.currentHeight || 0) + 10,
    paddingBottom: 20,
    paddingHorizontal: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0D3B66',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E1E8ED',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F8FA',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: '#1C1C1E',
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F5F8FA',
    marginRight: 6,
  },
  filterButtonActive: {
    backgroundColor: '#0D3B66',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#8E9297',
  },
  filterButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  statsRow: {
    paddingVertical: 16,
  },
  statsText: {
    fontSize: 14,
    color: '#8E9297',
    fontWeight: '500',
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E1E8ED',
    shadowColor: '#000',
    shadowOffset: {
      width: -2,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#0D3B66',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userInitials: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#8E9297',
    marginBottom: 8,
    flexShrink: 1,
  },
  userMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  roleText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  locationBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  locationText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F8FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F5F8FA',
    gap: 8,
  },
  userStat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userStatText: {
    fontSize: 12,
    color: '#8E9297',
    marginLeft: 6,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E9297',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#B8C5D1',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#8E9297',
    textAlign: 'center',
  },
});

export default UsersManagementScreen;
