-- ================================
-- TEST DATA
-- ================================
-- Based on DATABASE-SYSTEM.md specifications
-- Created: 2025-01-27

-- ================================
-- 1. USERS
-- ================================

-- Önce mevcut test kullanıcılarını temizle (eğer varsa)
DELETE FROM users WHERE email IN ('<EMAIL>', '<EMAIL>');

INSERT INTO users (id, role, full_name, email, phone, status, created_at, last_login) VALUES
-- Admin users - Sabit UUID'ler kullanarak
('550e8400-e29b-41d4-a716-************', 'ADMIN', '<PERSON><PERSON>', '<EMAIL>', '+49123456789', 'ACTIVE', NOW(), NOW() - INTERVAL '2 hours'),
('550e8400-e29b-41d4-a716-************', 'ADMIN', 'Sistem Yöneticisi', '<EMAIL>', '+49123456790', 'ACTIVE', NOW(), NOW() - INTERVAL '1 hour'),

-- Driver users
(gen_random_uuid(), 'DRIVER', 'Mehmet Şoför', '<EMAIL>', '+49123456791', 'ACTIVE', NOW(), NOW() - INTERVAL '3 hours'),
(gen_random_uuid(), 'DRIVER', 'Ali Sürücü', '<EMAIL>', '+49123456792', 'ACTIVE', NOW(), NOW() - INTERVAL '5 hours'),
(gen_random_uuid(), 'DRIVER', 'Hasan Taşıyıcı', '<EMAIL>', '+49123456793', 'ACTIVE', NOW(), NOW() - INTERVAL '1 day'),
(gen_random_uuid(), 'DRIVER', 'Mustafa Nakil', '<EMAIL>', '+49123456794', 'ON_LEAVE', NOW(), NOW() - INTERVAL '3 days'),

-- Family users
(gen_random_uuid(), 'FAMILY', 'Ayşe Yılmaz', '<EMAIL>', '+49123456795', 'ACTIVE', NOW(), NOW() - INTERVAL '6 hours'),
(gen_random_uuid(), 'FAMILY', 'Fatma Kaya', '<EMAIL>', '+49123456796', 'ACTIVE', NOW(), NOW() - INTERVAL '12 hours'),
(gen_random_uuid(), 'FAMILY', 'Zeynep Demir', '<EMAIL>', '+49123456797', 'ACTIVE', NOW(), NOW() - INTERVAL '2 days'),
(gen_random_uuid(), 'FAMILY', 'Hatice Çelik', '<EMAIL>', '+49123456798', 'ACTIVE', NOW(), NOW() - INTERVAL '4 days'),
(gen_random_uuid(), 'FAMILY', 'Emine Şahin', '<EMAIL>', '+49123456799', 'ACTIVE', NOW(), NOW() - INTERVAL '1 week');

-- ================================
-- 2. PLACES
-- ================================

INSERT INTO places (id, place_type, name, address, lat, lng, country, created_at) VALUES
-- Morgues in Germany
(gen_random_uuid(), 'MORGUE', 'Berlin Merkez Morgağı', 'Turmstraße 21, 10559 Berlin', 52.5200, 13.4050, 'DE', NOW()),
(gen_random_uuid(), 'MORGUE', 'Hamburg Şehir Morgağı', 'Butenfeld 34, 22529 Hamburg', 53.5511, 9.9937, 'DE', NOW()),
(gen_random_uuid(), 'MORGUE', 'München Belediye Morgağı', 'Thalkirchner Str. 48, 80337 München', 48.1351, 11.5820, 'DE', NOW()),

-- Airports
(gen_random_uuid(), 'AIRPORT', 'Berlin Brandenburg Havaalanı', 'Willy-Brandt-Platz, 12529 Schönefeld', 52.3667, 13.5033, 'DE', NOW()),
(gen_random_uuid(), 'AIRPORT', 'Hamburg Havaalanı', 'Flughafenstr. 1-3, 22335 Hamburg', 53.6304, 9.9882, 'DE', NOW()),
(gen_random_uuid(), 'AIRPORT', 'München Havaalanı', 'Nordallee 25, 85356 München', 48.3538, 11.7861, 'DE', NOW()),
(gen_random_uuid(), 'AIRPORT', 'İstanbul Havaalanı', 'Tayakadın Mahallesi, 34283 Arnavutköy/İstanbul', 41.2619, 28.7419, 'TR', NOW()),
(gen_random_uuid(), 'AIRPORT', 'Sabiha Gökçen Havaalanı', 'Sanayi Mahallesi, 34906 Pendik/İstanbul', 40.8986, 29.3092, 'TR', NOW()),

-- Consulates
(gen_random_uuid(), 'CONSULATE', 'Türkiye Konsolosluğu Berlin', 'Rungestraße 9, 10179 Berlin', 52.5139, 13.4122, 'DE', NOW()),
(gen_random_uuid(), 'CONSULATE', 'Türkiye Konsolosluğu Hamburg', 'Mittelweg 150, 20148 Hamburg', 53.5753, 9.9712, 'DE', NOW()),
(gen_random_uuid(), 'CONSULATE', 'Türkiye Konsolosluğu München', 'Möhlstraße 30, 81675 München', 48.1467, 11.6094, 'DE', NOW()),

-- Cemeteries in Turkey
(gen_random_uuid(), 'CEMETERY', 'Karacaahmet Mezarlığı', 'Karacaahmet Mahallesi, 34662 Üsküdar/İstanbul', 41.0082, 29.0181, 'TR', NOW()),
(gen_random_uuid(), 'CEMETERY', 'Zincirlikuyu Mezarlığı', 'Zincirlikuyu Mahallesi, 34394 Şişli/İstanbul', 41.0766, 28.9897, 'TR', NOW()),
(gen_random_uuid(), 'CEMETERY', 'Ankara Cebeci Mezarlığı', 'Cebeci Mahallesi, 06230 Altındağ/Ankara', 39.9334, 32.8597, 'TR', NOW()),

-- Home addresses
(gen_random_uuid(), 'HOME', 'Yılmaz Ailesi Evi', 'Kantstraße 12, 10623 Berlin', 52.5074, 13.3249, 'DE', NOW()),
(gen_random_uuid(), 'HOME', 'Kaya Ailesi Evi', 'Grindelallee 43, 20146 Hamburg', 53.5676, 9.9812, 'DE', NOW()),
(gen_random_uuid(), 'HOME', 'Demir Ailesi Evi', 'Leopoldstraße 85, 80802 München', 48.1549, 11.5418, 'DE', NOW());

-- ================================
-- 3. DECEASED
-- ================================

INSERT INTO deceased (id, ditib_member_id, full_name, nationality, gender, date_of_death, place_of_death, place_of_burial, family_name, family_email, family_phone, created_at) VALUES
(gen_random_uuid(), 'DITIB001', 'Ahmet Yılmaz', 'Türk', 'Erkek', '2025-01-20', 'Berlin Şehir Hastanesi', 'Karacaahmet Mezarlığı', 'Yılmaz', '<EMAIL>', '+49123456795', NOW()),
(gen_random_uuid(), 'DITIB002', 'Mehmet Kaya', 'Türk', 'Erkek', '2025-01-22', 'Hamburg Üniversite Hastanesi', 'Zincirlikuyu Mezarlığı', 'Kaya', '<EMAIL>', '+49123456796', NOW()),
(gen_random_uuid(), 'DITIB003', 'Hüseyin Demir', 'Türk', 'Erkek', '2025-01-24', 'München Klinikum', 'Ankara Cebeci Mezarlığı', 'Demir', '<EMAIL>', '+49123456797', NOW()),
(gen_random_uuid(), 'DITIB004', 'İbrahim Çelik', 'Türk', 'Erkek', '2025-01-25', 'Köln Hastanesi', 'Karacaahmet Mezarlığı', 'Çelik', '<EMAIL>', '+49123456798', NOW()),
(gen_random_uuid(), 'DITIB005', 'Osman Şahin', 'Türk', 'Erkek', '2025-01-26', 'Frankfurt Hastanesi', 'Zincirlikuyu Mezarlığı', 'Şahin', '<EMAIL>', '+49123456799', NOW());

-- ================================
-- 4. CASES
-- ================================

-- Get user IDs for family members
WITH family_users AS (
  SELECT id, email FROM users WHERE role = 'FAMILY'
),
deceased_records AS (
  SELECT id, family_email FROM deceased
)
INSERT INTO cases (id, deceased_id, family_user_id, status, burial_type, created_at)
SELECT 
  gen_random_uuid(),
  d.id,
  fu.id,
  CASE 
    WHEN RANDOM() < 0.3 THEN 'CLOSED'::case_status
    WHEN RANDOM() < 0.1 THEN 'CANCELLED'::case_status
    ELSE 'OPEN'::case_status
  END,
  'TR'::burial_type,
  NOW() - INTERVAL '1 day' * FLOOR(RANDOM() * 7)
FROM deceased_records dr
JOIN deceased d ON d.id = dr.id
JOIN family_users fu ON fu.email = dr.family_email;

-- ================================
-- 5. TASKS
-- ================================

-- Get necessary IDs for task creation
WITH case_data AS (
  SELECT c.id as case_id FROM cases c WHERE c.status = 'OPEN'
),
driver_data AS (
  SELECT id FROM users WHERE role = 'DRIVER' AND status = 'ACTIVE'
),
place_data AS (
  SELECT 
    id,
    place_type,
    ROW_NUMBER() OVER (PARTITION BY place_type ORDER BY RANDOM()) as rn
  FROM places
)
INSERT INTO tasks (id, case_id, assignee_id, task_type, status, origin_place_id, dest_place_id, scheduled_at, started_at, completed_at, est_distance_km, est_duration_min, notes, created_at)
SELECT
  gen_random_uuid(),
  cd.case_id,
  (SELECT id FROM driver_data ORDER BY RANDOM() LIMIT 1),
  'PICK_UP_FROM_MORGUE'::task_type,
  CASE
    WHEN RANDOM() < 0.2 THEN 'COMPLETED'::task_status
    WHEN RANDOM() < 0.1 THEN 'IN_PROGRESS'::task_status
    ELSE 'ASSIGNED'::task_status
  END,
  (SELECT id FROM place_data WHERE place_type = 'MORGUE' AND rn = 1),
  (SELECT id FROM place_data WHERE place_type = 'AIRPORT' AND rn = 1),
  NOW() + INTERVAL '1 hour' * FLOOR(RANDOM() * 24),
  CASE WHEN RANDOM() < 0.3 THEN NOW() - INTERVAL '1 hour' * FLOOR(RANDOM() * 12) ELSE NULL END,
  CASE WHEN RANDOM() < 0.2 THEN NOW() - INTERVAL '30 minutes' * FLOOR(RANDOM() * 6) ELSE NULL END,
  15.5 + RANDOM() * 20,
  30 + FLOOR(RANDOM() * 60),
  'Test görevi - otomatik oluşturuldu',
  NOW() - INTERVAL '1 hour' * FLOOR(RANDOM() * 48)
FROM case_data cd;

-- ================================
-- 6. DOCUMENTS
-- ================================

-- Add sample documents for cases
WITH case_data AS (
  SELECT id FROM cases LIMIT 3
),
admin_data AS (
  SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1
)
INSERT INTO documents (id, case_id, doc_type, storage_path, uploaded_by, created_at)
SELECT 
  gen_random_uuid(),
  cd.id,
  (ARRAY['DEATH_CERT', 'FORMUL_C', 'CARGO_WAYBILL', 'PHOTO'])[FLOOR(RANDOM() * 4 + 1)]::document_type,
  '/storage/documents/' || gen_random_uuid()::text || '.pdf',
  ad.id,
  NOW() - INTERVAL '1 hour' * FLOOR(RANDOM() * 24)
FROM case_data cd
CROSS JOIN admin_data ad
CROSS JOIN generate_series(1, 2);

-- ================================
-- 7. NOTIFICATIONS
-- ================================

-- Add sample notifications
WITH case_data AS (
  SELECT id FROM cases LIMIT 5
)
INSERT INTO notifications (id, case_id, channel, recipient, template, sent_at, status)
SELECT 
  gen_random_uuid(),
  cd.id,
  (ARRAY['PUSH', 'SMS', 'EMAIL'])[FLOOR(RANDOM() * 3 + 1)]::notification_channel,
  '<EMAIL>',
  'Cenaze işleminiz hakkında bilgilendirme: Görev atandı.',
  NOW() - INTERVAL '1 hour' * FLOOR(RANDOM() * 12),
  CASE WHEN RANDOM() < 0.9 THEN 'SENT'::notification_status ELSE 'FAIL'::notification_status END
FROM case_data cd;

-- ================================
-- 8. LOOKUP VALUES
-- ================================

INSERT INTO lookup_values (category, code, label_tr, label_de) VALUES
('COUNTRY_CODES', 'TR', 'Türkiye', 'Türkei'),
('COUNTRY_CODES', 'DE', 'Almanya', 'Deutschland'),
('PRIORITY_LEVELS', 'LOW', 'Düşük', 'Niedrig'),
('PRIORITY_LEVELS', 'MEDIUM', 'Orta', 'Mittel'),
('PRIORITY_LEVELS', 'HIGH', 'Yüksek', 'Hoch'),
('PRIORITY_LEVELS', 'URGENT', 'Acil', 'Dringend'),
('DOCUMENT_STATUS', 'PENDING', 'Beklemede', 'Ausstehend'),
('DOCUMENT_STATUS', 'APPROVED', 'Onaylandı', 'Genehmigt'),
('DOCUMENT_STATUS', 'REJECTED', 'Reddedildi', 'Abgelehnt');

-- ================================
-- 9. DRIVER LOCATIONS (Sample)
-- ================================

-- Add sample driver locations for in-progress tasks
WITH active_tasks AS (
  SELECT t.id as task_id, t.assignee_id as driver_id
  FROM tasks t 
  WHERE t.status = 'IN_PROGRESS' 
  LIMIT 2
),
location_points AS (
  SELECT 
    52.5200 + (RANDOM() - 0.5) * 0.1 as lat,
    13.4050 + (RANDOM() - 0.5) * 0.1 as lng
  FROM generate_series(1, 10)
)
INSERT INTO driver_locations (driver_id, task_id, lat, lng, recorded_at, distance_to_dest_km, eta_min)
SELECT 
  at.driver_id,
  at.task_id,
  lp.lat,
  lp.lng,
  NOW() - INTERVAL '1 minute' * (ROW_NUMBER() OVER () * 5),
  5.0 + RANDOM() * 10,
  15 + FLOOR(RANDOM() * 30)
FROM active_tasks at
CROSS JOIN location_points lp;

-- ================================
-- 10. TASK EVENTS (Sample)
-- ================================

-- Add sample task events
WITH task_data AS (
  SELECT t.id as task_id, t.assignee_id as driver_id, t.status
  FROM tasks t 
  WHERE t.assignee_id IS NOT NULL
  LIMIT 5
)
INSERT INTO task_events (task_id, actor_id, event_type, old_status, new_status, recorded_at)
SELECT 
  td.task_id,
  td.driver_id,
  'STATUS_CHANGE'::task_event_type,
  NULL,
  'ASSIGNED'::task_status,
  NOW() - INTERVAL '1 hour' * FLOOR(RANDOM() * 24)
FROM task_data td;
