-- ================================
-- FIX RLS POLICY FOR LAST_LOGIN UPDATE
-- ================================
-- Bu dosya last_login güncelleme sorununu çözer

-- Mevcut UPDATE politikasını kaldır
DROP POLICY IF EXISTS "Users can update own profile" ON users;

-- <PERSON>ail bazlı UPDATE politikası ekle
CREATE POLICY "Users can update own profile by email" ON users
  FOR UPDATE USING (
    auth.email() = email OR 
    auth.uid()::text = id::text
  );

-- Alternatif: Daha esnek politika (authenticated kullanıcılar kendi email'lerini güncelleyebilir)
-- DROP POLICY IF EXISTS "Users can update own profile by email" ON users;
-- CREATE POLICY "Allow authenticated users to update by email" ON users
--   FOR UPDATE USING (
--     auth.uid() IS NOT NULL AND 
--     auth.email() = email
--   );

-- Test için: Politika durumunu kontrol et
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'users' AND cmd = 'UPDATE';
